import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho response chi tiết MCP system
 */
export class McpSystemDetailDto {
  /**
   * UUID của MCP system
   */
  @ApiProperty({
    description: 'UUID của MCP system',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  /**
   * Tên server MCP
   */
  @ApiProperty({
    description: 'Tên server MCP',
    example: 'filesystem-server',
  })
  nameServer: string;

  /**
   * Mô tả về MCP system
   */
  @ApiPropertyOptional({
    description: 'Mô tả về MCP system',
    example: 'Server MCP để quản lý hệ thống file',
  })
  description?: string;

  /**
   * Cấu hình MCP dạng JSON
   */
  @ApiProperty({
    description: 'Cấu hình MCP dạng JSON',
    example: {
      command: 'node',
      args: ['server.js'],
      env: {
        NODE_ENV: 'production'
      }
    },
  })
  config: Record<string, any>;
}

/**
 * DTO cho item trong danh sách MCP systems
 */
export class McpSystemListItemDto {
  /**
   * UUID của MCP system
   */
  @ApiProperty({
    description: 'UUID của MCP system',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  /**
   * Tên server MCP
   */
  @ApiProperty({
    description: 'Tên server MCP',
    example: 'filesystem-server',
  })
  nameServer: string;

  /**
   * Mô tả về MCP system
   */
  @ApiPropertyOptional({
    description: 'Mô tả về MCP system',
    example: 'Server MCP để quản lý hệ thống file',
  })
  description?: string;
}
