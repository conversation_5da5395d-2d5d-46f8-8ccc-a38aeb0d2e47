import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho item trong danh sách MCP systems đã xóa
 */
export class McpSystemTrashItemDto {
  /**
   * UUID của MCP system
   */
  @ApiProperty({
    description: 'UUID của MCP system',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  /**
   * Tên server MCP
   */
  @ApiProperty({
    description: 'Tên server MCP',
    example: 'filesystem-server',
  })
  nameServer: string;

  /**
   * Mô tả về MCP system
   */
  @ApiPropertyOptional({
    description: 'Mô tả về MCP system',
    example: 'Server MCP để quản lý hệ thống file',
  })
  description?: string;

  /**
   * C<PERSON>u hình MCP dạng JSON
   */
  @ApiProperty({
    description: 'C<PERSON>u hình MCP dạng JSON',
    example: {
      command: 'node',
      args: ['server.js'],
      env: {
        NODE_ENV: 'production'
      }
    },
  })
  config: Record<string, any>;

  /**
   * Thời điểm xóa (timestamp millis)
   */
  @ApiProperty({
    description: 'Thời điểm xóa (timestamp millis)',
    example: 1672531200000,
  })
  deletedAt: number;

  /**
   * ID nhân viên thực hiện xóa
   */
  @ApiPropertyOptional({
    description: 'ID nhân viên thực hiện xóa',
    example: 1,
  })
  deletedBy?: number;
}

/**
 * DTO cho response khôi phục MCP systems
 */
export class RestoreMcpSystemResponseDto {
  /**
   * Số lượng MCP systems đã khôi phục thành công
   */
  @ApiProperty({
    description: 'Số lượng MCP systems đã khôi phục thành công',
    example: 2,
  })
  restoredCount: number;

  /**
   * Danh sách ID của các MCP systems đã khôi phục thành công
   */
  @ApiProperty({
    description: 'Danh sách ID của các MCP systems đã khôi phục thành công',
    example: ['123e4567-e89b-12d3-a456-************', '456e7890-e89b-12d3-a456-426614174001'],
    type: [String],
  })
  restoredIds: string[];
}
