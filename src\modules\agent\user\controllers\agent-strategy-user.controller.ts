import { ApiResponseDto } from '@common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { AgentStrategyService } from '@modules/agent/user/services/agent-strategy.service';
import { JwtUserGuard } from '@modules/auth/guards';
import {
  Controller,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiTags
} from '@nestjs/swagger';

/**
 * Controller xử lý các API endpoint cho Strategy của Agent người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto
)
export class AgentStrategyUserController {
  constructor(private readonly agentStrategyService: AgentStrategyService) { }
}
