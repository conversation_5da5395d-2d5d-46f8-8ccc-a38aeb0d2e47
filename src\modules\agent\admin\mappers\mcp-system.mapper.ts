import { McpSystems } from '@modules/agent/entities';
import { McpSystemListItemDto } from '../dto/mcp-system';

/**
 * Mapper cho MCP System
 * Chuyển đổi giữa Entity và DTO
 */
export class McpSystemMapper {
  /**
   * Chuyển đổi từ Entity sang ListItemDto
   * @param entity McpSystems entity
   * @returns McpSystemListItemDto
   */
  static toListItemDto(entity: McpSystems): McpSystemListItemDto {
    return {
      id: entity.id,
      nameServer: entity.nameServer,
      description: entity.description,
      config: entity.config,
    };
  }

  /**
   * Chuyển đổi từ danh sách Entity sang danh sách ListItemDto
   * @param entities Danh sách McpSystems entities
   * @returns Danh sách McpSystemListItemDto
   */
  static toListItemDtos(entities: McpSystems[]): McpSystemListItemDto[] {
    return entities.map(entity => this.toListItemDto(entity));
  }
}
