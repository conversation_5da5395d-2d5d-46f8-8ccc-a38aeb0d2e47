import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  Max,
  <PERSON><PERSON>,
  <PERSON>,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho cấu hình model
 */
export class ModelConfigBlockDto {
  /**
   * Nhiệt độ (temperature)
   */
  @ApiProperty({
    description: 'Nhiệt độ (temperature)',
    example: 0.7,
  })
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature: number;

  /**
   * Top P
   */
  @ApiProperty({
    description: 'Top P',
    example: 0.9,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  top_p: number;

  /**
   * Top K
   */
  @ApiProperty({
    description: 'Top K',
    example: 40,
  })
  @IsNumber()
  @Min(0)
  top_k: number;

  /**
   * <PERSON><PERSON> token tối đa
   */
  @ApiProperty({
    description: 'Số token tối đa',
    example: 1000,
  })
  @IsNumber()
  @Min(1)
  max_tokens: number;
}

/**
 * DTO cho block Profile Agent (enableAgentProfileCustomization)
 */
export class ProfileBlockDto {
  /**
   * Có kích hoạt tùy chỉnh profile không
   */
  @ApiProperty({
    description: 'Có kích hoạt tùy chỉnh profile không',
    example: true,
  })
  @IsBoolean()
  enabled: boolean;

  /**
   * Giới tính
   */
  @ApiPropertyOptional({
    description: 'Giới tính',
    example: 'MALE',
  })
  @IsString()
  @IsOptional()
  gender?: string;

  /**
   * Ngày sinh (timestamp millis)
   */
  @ApiPropertyOptional({
    description: 'Ngày sinh (timestamp millis)',
    example: 946684800000,
  })
  @IsNumber()
  @IsOptional()
  dateOfBirth?: number;

  /**
   * Vị trí
   */
  @ApiPropertyOptional({
    description: 'Vị trí',
    example: 'Trợ lý AI',
  })
  @IsString()
  @IsOptional()
  position?: string;

  /**
   * Học vấn
   */
  @ApiPropertyOptional({
    description: 'Học vấn',
    example: 'Đại học',
  })
  @IsString()
  @IsOptional()
  education?: string;

  /**
   * Kỹ năng
   */
  @ApiPropertyOptional({
    description: 'Kỹ năng',
    example: ['Trả lời câu hỏi', 'Tìm kiếm thông tin'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  skills?: string[];

  /**
   * Tính cách
   */
  @ApiPropertyOptional({
    description: 'Tính cách',
    example: ['Thân thiện', 'Kiên nhẫn'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  personality?: string[];

  /**
   * Ngôn ngữ
   */
  @ApiPropertyOptional({
    description: 'Ngôn ngữ',
    example: ['Tiếng Việt', 'Tiếng Anh'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  languages?: string[];

  /**
   * Quốc gia
   */
  @ApiPropertyOptional({
    description: 'Quốc gia',
    example: 'Việt Nam',
  })
  @IsString()
  @IsOptional()
  nations?: string;
}

/**
 * DTO cho block Task Conversion Tracking (enableTaskConversionTracking)
 */
export class TaskConversionBlockDto {
  /**
   * Có kích hoạt theo dõi conversion không
   */
  @ApiProperty({
    description: 'Có kích hoạt theo dõi conversion không',
    example: true,
  })
  @IsBoolean()
  enabled: boolean;

  /**
   * Cấu hình chuyển đổi
   */
  @ApiPropertyOptional({
    description: 'Cấu hình chuyển đổi',
    type: 'object',
  })
  @IsObject()
  @IsOptional()
  convertConfig?: Record<string, any>;
}

/**
 * DTO cho block Dynamic Strategy (enableDynamicStrategyExecution)
 */
export class StrategyBlockDto {
  /**
   * Có kích hoạt strategy động không
   */
  @ApiProperty({
    description: 'Có kích hoạt strategy động không',
    example: true,
  })
  @IsBoolean()
  enabled: boolean;

  /**
   * ID của strategy
   */
  @ApiPropertyOptional({
    description: 'ID của strategy',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  strategyId?: string;
}

/**
 * DTO cho block Multi Agent Collaboration (enableMultiAgentCollaboration)
 */
export class MultiAgentBlockDto {
  /**
   * Có kích hoạt hợp tác đa agent không
   */
  @ApiProperty({
    description: 'Có kích hoạt hợp tác đa agent không',
    example: false,
  })
  @IsBoolean()
  enabled: boolean;

  /**
   * Danh sách ID của các agent khác để hợp tác
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của các agent khác để hợp tác',
    example: ['agent-1', 'agent-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  collaborationAgentIds?: string[];
}

/**
 * DTO cho block Output Messenger (enableOutputToMessenger)
 */
export class OutputMessengerBlockDto {
  /**
   * Có kích hoạt output tới Messenger không
   */
  @ApiProperty({
    description: 'Có kích hoạt output tới Messenger không',
    example: true,
  })
  @IsBoolean()
  enabled: boolean;

  /**
   * Danh sách ID của Facebook Pages
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của Facebook Pages',
    example: ['page-1', 'page-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  facebookPageIds?: string[];
}

/**
 * DTO cho block Output Website (enableOutputToWebsiteLiveChat)
 */
export class OutputWebsiteBlockDto {
  /**
   * Có kích hoạt output tới Website Live Chat không
   */
  @ApiProperty({
    description: 'Có kích hoạt output tới Website Live Chat không',
    example: true,
  })
  @IsBoolean()
  enabled: boolean;

  /**
   * Danh sách ID của Websites
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của Websites',
    example: ['website-1', 'website-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  websiteIds?: string[];
}

/**
 * DTO cho block Resource Usage (enableResourceUsage)
 */
export class ResourceBlockDto {
  /**
   * Có kích hoạt sử dụng tài nguyên không
   */
  @ApiProperty({
    description: 'Có kích hoạt sử dụng tài nguyên không',
    example: true,
  })
  @IsBoolean()
  enabled: boolean;

  /**
   * Danh sách ID của media
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của media',
    example: ['media-1', 'media-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  mediaIds?: string[];

  /**
   * Danh sách ID của sản phẩm
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của sản phẩm',
    example: ['1', '2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  productIds?: string[];

  /**
   * Danh sách ID của URL
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của URL',
    example: ['url-1', 'url-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  urlIds?: string[];

  /**
   * ID của vector store
   */
  @ApiPropertyOptional({
    description: 'ID của vector store',
    example: 'vector-store-1',
  })
  @IsString()
  @IsOptional()
  vectorStoreId?: string;

  /**
   * Danh sách ID của user custom tools
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của user custom tools',
    example: ['tool-uuid-1', 'tool-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  customToolIds?: string[];
}

/**
 * DTO chính cho việc tạo agent theo cấu trúc modular v2
 */
export class CreateAgentModularV2Dto {
  /**
   * Tên agent
   */
  @ApiProperty({
    description: 'Tên agent',
    example: 'My Assistant V2',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  /**
   * ID loại agent
   */
  @ApiProperty({
    description: 'ID loại agent',
    example: 1,
  })
  @IsNumber()
  typeId: number;

  /**
   * MIME type của avatar
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * Cấu hình model
   */
  @ApiProperty({
    description: 'Cấu hình model',
    type: ModelConfigBlockDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigBlockDto)
  modelConfig: ModelConfigBlockDto;

  /**
   * Hướng dẫn (instruction)
   */
  @ApiProperty({
    description: 'Hướng dẫn (instruction)',
    example: 'Bạn là trợ lý cá nhân, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  instruction: string;

  /**
   * Block Profile Agent
   */
  @ApiProperty({
    description: 'Cấu hình profile agent',
    type: ProfileBlockDto,
  })
  @ValidateNested()
  @Type(() => ProfileBlockDto)
  profileBlock: ProfileBlockDto;

  /**
   * Block Task Conversion Tracking
   */
  @ApiProperty({
    description: 'Cấu hình theo dõi conversion',
    type: TaskConversionBlockDto,
  })
  @ValidateNested()
  @Type(() => TaskConversionBlockDto)
  taskConversionBlock: TaskConversionBlockDto;

  /**
   * Block Dynamic Strategy
   */
  @ApiProperty({
    description: 'Cấu hình strategy động',
    type: StrategyBlockDto,
  })
  @ValidateNested()
  @Type(() => StrategyBlockDto)
  strategyBlock: StrategyBlockDto;

  /**
   * Block Multi Agent Collaboration
   */
  @ApiProperty({
    description: 'Cấu hình hợp tác đa agent',
    type: MultiAgentBlockDto,
  })
  @ValidateNested()
  @Type(() => MultiAgentBlockDto)
  multiAgentBlock: MultiAgentBlockDto;

  /**
   * Block Output Messenger
   */
  @ApiProperty({
    description: 'Cấu hình output tới Messenger',
    type: OutputMessengerBlockDto,
  })
  @ValidateNested()
  @Type(() => OutputMessengerBlockDto)
  outputMessengerBlock: OutputMessengerBlockDto;

  /**
   * Block Output Website
   */
  @ApiProperty({
    description: 'Cấu hình output tới Website',
    type: OutputWebsiteBlockDto,
  })
  @ValidateNested()
  @Type(() => OutputWebsiteBlockDto)
  outputWebsiteBlock: OutputWebsiteBlockDto;

  /**
   * Block Resource Usage
   */
  @ApiProperty({
    description: 'Cấu hình sử dụng tài nguyên',
    type: ResourceBlockDto,
  })
  @ValidateNested()
  @Type(() => ResourceBlockDto)
  resourceBlock: ResourceBlockDto;

  /**
   * ID của base model (system model)
   */
  @ApiPropertyOptional({
    description: 'ID của base model (system model)',
    example: 'base-model-uuid',
  })
  @IsString()
  @IsOptional()
  model_base_id?: string;

  /**
   * ID của finetuning model
   */
  @ApiPropertyOptional({
    description: 'ID của finetuning model',
    example: 'finetuning-model-uuid',
  })
  @IsString()
  @IsOptional()
  model_finetuning_id?: string;

  /**
   * ID của model cá nhân từ provider
   */
  @ApiPropertyOptional({
    description: 'ID của model cá nhân từ provider (phải đi kèm với provider_id)',
    example: 'gpt-4o',
  })
  @IsString()
  @IsOptional()
  model_id?: string;

  /**
   * ID của provider cá nhân
   */
  @ApiPropertyOptional({
    description: 'ID của provider cá nhân (phải đi kèm với model_id)',
    example: 'provider-uuid',
  })
  @IsString()
  @IsOptional()
  provider_id?: string;
}
