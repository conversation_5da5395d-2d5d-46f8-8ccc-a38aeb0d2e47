import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import {
  AgentMediaRepository,
  AgentProductRepository,
  AgentRankRepository,
  AgentRepository,
  AgentUserRepository,
  TypeAgentRepository,
  UserMultiAgentRepository
} from '@modules/agent/repositories';

import { UserProductRepository } from '@/modules/business/repositories';
import { VectorStoreRepository } from '@modules/data/knowledge-files/repositories';
import { MediaRepository } from '@modules/data/media/repositories';
import { UrlRepository } from '@modules/data/url/repositories';
import { FacebookPageRepository, UserWebsiteRepository } from '@modules/integration/repositories';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { FacebookService } from '@shared/services/facebook/facebook.service';
import { S3Service } from '@shared/services/s3.service';
import { Transactional } from 'typeorm-transactional';
import { CreateAgentDto, CreateAgentResponseDto } from '../dto/agent';
import { TypeAgentValidationHelper } from '../helpers/type-agent-validation.helper';


/**
 * Service xử lý các thao tác liên quan đến agent cho người dùng
 */
@Injectable()
export class AgentUserService {
  private readonly logger = new Logger(AgentUserService.name);

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly agentUserRepository: AgentUserRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly agentMediaRepository: AgentMediaRepository,
    private readonly agentProductRepository: AgentProductRepository,
    private readonly vectorStoreRepository: VectorStoreRepository,
    private readonly mediaRepository: MediaRepository,
    private readonly urlRepository: UrlRepository,
    private readonly userProductRepository: UserProductRepository,
    private readonly userWebsiteRepository: UserWebsiteRepository,
    private readonly facebookPageRepository: FacebookPageRepository,
    private readonly facebookService: FacebookService,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly agentRankRepository: AgentRankRepository,
    private readonly userMultiAgentRepository: UserMultiAgentRepository,
    private readonly typeAgentValidationHelper: TypeAgentValidationHelper,
  ) { }

  /**
   * Tạo agent mới dựa trên TypeAgentConfig
   * @param userId ID của người dùng
   * @param createDto Thông tin agent mới
   * @returns Thông tin tạo agent thành công
   */
  @Transactional()
  async createAgent(
    userId: number,
    createDto: CreateAgentDto,
  ): Promise<CreateAgentResponseDto> {
    try {
      this.logger.log(`Bắt đầu tạo agent cho user ${userId}: ${createDto.name}`);

      // 1. Lấy TypeAgent và TypeAgentConfig từ typeId
      const typeAgent = await this.typeAgentRepository.findById(createDto.typeId);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      const typeAgentConfig = typeAgent.config; // TypeAgentConfig từ trường config
      this.logger.log(`TypeAgentConfig: ${JSON.stringify(typeAgentConfig)}`);

      // 2. Validate dữ liệu dựa trên TypeAgentConfig
      await this.validateAgentDataByConfig(createDto, typeAgentConfig);

      // 3. Kiểm tra tên agent đã tồn tại cho user này chưa
      const existingAgent = await this.agentRepository.findByNameAndUserId(createDto.name, userId);
      if (existingAgent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NAME_EXISTS);
      }

      // 4. Tạo agent entity cơ bản
      const agent = await this.createAgentEntity(createDto, typeAgent, userId);

      // 5. Xử lý các blocks dữ liệu theo TypeAgentConfig
      await this.processAgentBlocks(agent, createDto, typeAgentConfig, userId);

      // 6. Tạo S3 key cho avatar nếu có
      let avatarUploadUrl: string | undefined;
      if (createDto.avatarMimeType) {
        avatarUploadUrl = await this.generateAvatarUploadUrl(agent.id, createDto.avatarMimeType);
      }

      this.logger.log(`Đã tạo agent thành công: ${agent.id}`);

      return {
        id: agent.id,
        avatarUploadUrl,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED, error.message);
    }
  }

  /**
   * Validate dữ liệu agent dựa trên TypeAgentConfig
   * @param createDto DTO tạo agent
   * @param config TypeAgentConfig
   */
  private async validateAgentDataByConfig(
    createDto: CreateAgentDto,
    config: any, // TypeAgentConfig
  ): Promise<void> {
    // Kiểm tra các trường bắt buộc khi tính năng được enable
    if (config.enableAgentProfileCustomization && !createDto.profileData) {
      throw new AppException(AGENT_ERROR_CODES.PROFILE_DATA_REQUIRED);
    }

    if (config.enableTaskConversionTracking && !createDto.taskConversionData) {
      throw new AppException(AGENT_ERROR_CODES.TASK_CONVERSION_DATA_REQUIRED);
    }

    if (config.enableDynamicStrategyExecution && !createDto.strategyData) {
      throw new AppException(AGENT_ERROR_CODES.STRATEGY_DATA_REQUIRED);
    }

    if (config.enableMultiAgentCollaboration && !createDto.multiAgentData) {
      throw new AppException(AGENT_ERROR_CODES.MULTI_AGENT_DATA_REQUIRED);
    }

    if (config.enableOutputToMessenger && !createDto.outputMessengerData) {
      throw new AppException(AGENT_ERROR_CODES.OUTPUT_MESSENGER_DATA_REQUIRED);
    }

    if (config.enableOutputToWebsiteLiveChat && !createDto.outputWebsiteData) {
      throw new AppException(AGENT_ERROR_CODES.OUTPUT_WEBSITE_DATA_REQUIRED);
    }

    if (config.enableResourceUsage && !createDto.resourceData) {
      throw new AppException(AGENT_ERROR_CODES.RESOURCE_DATA_REQUIRED);
    }

    // TODO: Thêm validation chi tiết cho từng block
  }

  /**
   * Lấy danh sách agent đơn giản của người dùng (chỉ id, avatar, name)
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn với phân trang
   * @returns Danh sách agent đơn giản có phân trang
   */
  // async getSimpleAgentList(
  //   userId: number,
  //   queryDto: AgentSimpleQueryDto
  // ): Promise<PaginatedResult<AgentSimpleListDto>> {
  //   try {
  //     // Lấy danh sách agent từ repository với phân trang
  //     const result = await this.agentRepository.findSimpleListByUserIdPaginated(userId, queryDto);

  //     // Chuyển đổi sang DTO và gán CDN URL cho avatar
  //     const items = result.items.map(agent => ({
  //       id: agent.id,
  //       avatar: agent.avatar ? this.cdnService.generateUrlView(agent.avatar, TimeIntervalEnum.ONE_DAY) || undefined : undefined,
  //       name: agent.name,
  //     }));

  //     return {
  //       items,
  //       meta: result.meta,
  //     };
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi lấy danh sách agent đơn giản: ${error.message}`);
  //     throw new AppException(
  //       AGENT_ERROR_CODES.AGENT_LIST_QUERY_FAILED,
  //       error.message,
  //     );
  //   }
  // }

  // /**
  //  * Lấy danh sách agent của người dùng
  //  * @param userId ID của người dùng
  //  * @param queryDto Tham số truy vấn
  //  * @returns Danh sách agent có phân trang
  //  */
  // async getAgents(
  //   userId: number,
  //   queryDto: AgentQueryDto,
  // ): Promise<PaginatedResult<AgentListItemDto>> {
  //   try {
  //     // Sử dụng repository để lấy danh sách agent
  //     const result = await this.agentUserRepository.findPaginated(
  //       userId,
  //       queryDto,
  //     );

  //     // Lấy danh sách tất cả cấp bậc
  //     const ranks = await this.agentRankRepository.findAll();

  //     // Chuyển đổi kết quả sang DTO (model_id đã được resolve trong repository)
  //     const agentItems = await Promise.all(result.items.map(async (item: any) => {
  //       try {
  //         // Tìm cấp bậc phù hợp dựa trên exp
  //         const exp = item.exp || 0;
  //         const rank = ranks.find(r => exp >= r.minExp && exp < r.maxExp) || ranks[0];

  //         // Tạo URL CDN cho avatar nếu có
  //         let avatarUrl = item.avatar || null;
  //         if (item.avatar) {
  //           try {
  //             avatarUrl = this.cdnService.generateUrlView(item.avatar, TimeIntervalEnum.ONE_DAY);
  //           } catch (error) {
  //             this.logger.warn(`Không thể tạo URL CDN cho avatar: ${error.message}`);
  //           }
  //         }

  //         // Tạo URL CDN cho badge nếu có
  //         let badgeUrl = '';
  //         if (rank && rank.badge) {
  //           try {
  //             badgeUrl = this.cdnService.generateUrlView(rank.badge, TimeIntervalEnum.ONE_DAY) || '';
  //           } catch (error) {
  //             this.logger.warn(`Không thể tạo URL CDN cho badge: ${error.message}`);
  //           }
  //         }

  //         return {
  //           id: item.id,
  //           name: item.name,
  //           avatar: avatarUrl,
  //           typeId: item.typeId,
  //           typeName: item.typeName || null,
  //           active: item.active || false,
  //           createdAt: item.createdAt,
  //           updatedAt: item.updatedAt,
  //           exp: item.exp || 0,
  //           expMax: rank ? rank.maxExp : 100,
  //           level: rank ? rank.id : 1,
  //           badge_url: badgeUrl,
  //           model_id: item.resolvedModelId || null,
  //         };
  //       } catch (error) {
  //         this.logger.error(`Lỗi khi xử lý thông tin agent ${item.id}: ${error.message}`);

  //         throw new AppException(
  //           AGENT_ERROR_CODES.AGENT_LIST_QUERY_FAILED,
  //           `Lỗi khi xử lý thông tin agent ${item.id}: ${error.message}`,
  //         );
  //       }
  //     }));

  //     return {
  //       items: agentItems,
  //       meta: result.meta,
  //     };
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi lấy danh sách agent: ${error.message}`);
  //     throw new AppException(
  //       AGENT_ERROR_CODES.AGENT_LIST_QUERY_FAILED,
  //       error.message,
  //     );
  //   }
  // }

  // /**
  //  * Lấy chi tiết agent
  //  * @param id ID của agent
  //  * @param userId ID của người dùng
  //  * @returns Chi tiết agent
  //  */
  // async getAgentDetail(id: string, userId: number): Promise<AgentDetailDto> {
  //   try {
  //     // Sử dụng repository để lấy thông tin agent và agent user
  //     const result = await this.agentUserRepository.findAgentByIdAndUserId(
  //       id,
  //       userId,
  //     );
  //     if (!result) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //     }

  //     const { agent, agentUser } = result;

  //     // Chuyển đổi kết quả sang DTO
  //     return await this.mapToAgentDetailDto(agent, agentUser);
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(`Lỗi khi lấy chi tiết agent: ${error.message}`);
  //     throw new AppException(
  //       AGENT_ERROR_CODES.AGENT_DETAIL_FAILED,
  //       error.message,
  //     );
  //   }
  // }

  // /**
  //  * Tạo agent mới
  //  * @param userId ID của người dùng
  //  * @param createDto Thông tin agent mới
  //  * @returns Thông tin tạo agent thành công
  //  */
  // // @Transactional()
  // // async createAgent(
  // //   userId: number,
  // //   createDto: CreateAgentDto,
  // // ): Promise<CreateAgentResponseDto> {
  // //   try {
  // //     // Kiểm tra tên agent đã tồn tại cho user này chưa
  // //     const nameExists = await this.agentRepository.existsByNameAndUserId(createDto.name, userId);
  // //     if (nameExists) {
  // //       throw new AppException(AGENT_ERROR_CODES.AGENT_NAME_EXISTS);
  // //     }

  // //     // Kiểm tra loại agent có tồn tại không
  // //     const typeAgent = await this.typeAgentRepository.findById(
  // //       createDto.typeId,
  // //     );
  // //     if (!typeAgent) {
  // //       throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
  // //     }

  // //     // Tạo agent mới với model config đơn giản
  // //     const modelConfig: ModelConfig = {
  // //       temperature: createDto.modelConfig.temperature,
  // //       top_p: createDto.modelConfig.top_p,
  // //       top_k: createDto.modelConfig.top_k,
  // //       max_tokens: createDto.modelConfig.max_tokens,
  // //     };

  // //     // Sử dụng agentRepository đã được inject
  // //     const agent = this.agentRepository.create({
  // //       name: createDto.name,
  // //       modelConfig,
  // //       instruction: createDto.instruction,
  // //       status: AgentStatusEnum.PENDING, // Mặc định là PENDING
  // //       // Thêm model reference fields
  // //       createdAt: Date.now(),
  // //       updatedAt: Date.now(),
  // //     });

  // //     // Tạo S3 key cho avatar nếu có
  // //     let avatarUploadUrl: string | null = null;
  // //     if (createDto.avatarMimeType) {
  // //       if (!userId || isNaN(userId)) {
  // //         throw new AppException(AGENT_ERROR_CODES.INVALID_S3_KEY, 'ID người dùng không hợp lệ');
  // //       }

  // //       const avatarKey = generateS3Key({
  // //         baseFolder: userId.toString(),
  // //         categoryFolder: CategoryFolderEnum.AGENT,
  // //       });

  // //       // Cập nhật avatar key cho agent
  // //       agent.avatar = avatarKey;

  // //       // Tạo URL để upload avatar
  // //       avatarUploadUrl = await this.s3Service.createPresignedWithID(
  // //         avatarKey,
  // //         TimeIntervalEnum.ONE_HOUR,
  // //         ImageType.getType(createDto.avatarMimeType),
  // //         FileSizeEnum.ONE_MB,
  // //       );
  // //     }

  // //     // Lưu agent
  // //     const savedAgent = await this.agentRepository.save(agent);

  // //     // Tạo agent user
  // //     // Chuyển đổi từ ProfileDto sang ProfileAgent
  // //     const profileAgent: ProfileAgent = {
  // //       gender: createDto.profile.gender ? GenderUtils.getGender(createDto.profile.gender.toUpperCase()) : undefined,
  // //       dateOfBirth: createDto.profile.dateOfBirth ? new Date(createDto.profile.dateOfBirth) : undefined,
  // //       position: createDto.profile.position,
  // //       education: createDto.profile.education,
  // //       skills: createDto.profile.skills,
  // //       personality: createDto.profile.personality,
  // //       languages: createDto.profile.languages,
  // //       nations: createDto.profile.nations,
  // //     };

  // //     const agentUser = this.agentUserRepository.create({
  // //       id: savedAgent.id,
  // //       userId,
  // //       typeId: createDto.typeId,
  // //       profile: profileAgent,
  // //     });

  // //     // Lưu agent user
  // //     await this.agentUserRepository.save(agentUser);

  // //     // Xử lý tài nguyên media nếu có
  // //     if (createDto.mediaIds && createDto.mediaIds.length > 0) {
  // //       // Kiểm tra tất cả media có tồn tại không
  // //       const existingMedias = await this.mediaRepository.find({
  // //         where: { id: In(createDto.mediaIds) }
  // //       });
  // //       const existingMediaIds = existingMedias.map(media => media.id);
  // //       const nonExistingMediaIds = createDto.mediaIds.filter(id => !existingMediaIds.includes(id));

  // //       if (nonExistingMediaIds.length > 0) {
  // //         throw new AppException(
  // //           AGENT_ERROR_CODES.MEDIA_NOT_FOUND,
  // //           `Không tìm thấy media với ID: ${nonExistingMediaIds.join(', ')}`
  // //         );
  // //       }

  // //       // Kiểm tra quyền truy cập media
  // //       const unauthorizedMedias = existingMedias.filter(media => media.ownedBy !== userId);
  // //       if (unauthorizedMedias.length > 0) {
  // //         throw new AppException(
  // //           AGENT_ERROR_CODES.MEDIA_NOT_FOUND,
  // //           `Không có quyền truy cập media với ID: ${unauthorizedMedias.map(m => m.id).join(', ')}`
  // //         );
  // //       }

  // //       // Thêm từng media vào agent
  // //       for (const mediaId of createDto.mediaIds) {
  // //         await this.agentMediaRepository.addMedia(savedAgent.id, mediaId);
  // //       }
  // //     }

  // //     // Xử lý tài nguyên product nếu có
  // //     if (createDto.productIds && createDto.productIds.length > 0) {
  // //       // Chuyển đổi productIds từ string sang number
  // //       const productIdsNumber = createDto.productIds.map(id => parseInt(id));

  // //       // Kiểm tra tất cả product có tồn tại không
  // //       const existingProducts = await this.userProductRepository.find({
  // //         where: { id: In(productIdsNumber) }
  // //       });

  // //       const existingProductIds = existingProducts.map(product => product.id.toString());
  // //       const nonExistingProductIds = createDto.productIds.filter(id => !existingProductIds.includes(id));

  // //       if (nonExistingProductIds.length > 0) {
  // //         throw new AppException(
  // //           AGENT_ERROR_CODES.PRODUCT_NOT_FOUND,
  // //           `Không tìm thấy sản phẩm với ID: ${nonExistingProductIds.join(', ')}`
  // //         );
  // //       }

  // //       // Kiểm tra quyền truy cập product
  // //       const unauthorizedProducts = existingProducts.filter(product => product.createdBy !== userId);
  // //       if (unauthorizedProducts.length > 0) {
  // //         throw new AppException(
  // //           AGENT_ERROR_CODES.PRODUCT_NOT_FOUND,
  // //           `Không có quyền truy cập sản phẩm với ID: ${unauthorizedProducts.map(p => p.id).join(', ')}`
  // //         );
  // //       }

  // //       // Thêm từng product vào agent
  // //       for (const productId of createDto.productIds) {
  // //         await this.agentProductRepository.addProduct(savedAgent.id, productId);
  // //       }
  // //     }

  // //     // Xử lý tài nguyên url nếu có
  // //     if (createDto.urlIds && createDto.urlIds.length > 0) {
  // //       // Kiểm tra tất cả URL có tồn tại không
  // //       const existingUrls = await this.urlRepository.find({
  // //         where: { id: In(createDto.urlIds) }
  // //       });
  // //       const existingUrlIds = existingUrls.map(url => url.id);
  // //       const nonExistingUrlIds = createDto.urlIds.filter(id => !existingUrlIds.includes(id));

  // //       if (nonExistingUrlIds.length > 0) {
  // //         throw new AppException(
  // //           AGENT_ERROR_CODES.URL_NOT_FOUND,
  // //           `Không tìm thấy URL với ID: ${nonExistingUrlIds.join(', ')}`
  // //         );
  // //       }

  // //       // Kiểm tra quyền truy cập URL
  // //       const unauthorizedUrls = existingUrls.filter(url => url.ownedBy !== userId);
  // //       if (unauthorizedUrls.length > 0) {
  // //         throw new AppException(
  // //           AGENT_ERROR_CODES.URL_NOT_FOUND,
  // //           `Không có quyền truy cập URL với ID: ${unauthorizedUrls.map(u => u.id).join(', ')}`
  // //         );
  // //       }

  // //       // Thêm từng URL vào agent
  // //       for (const urlId of createDto.urlIds) {
  // //         await this.agentUrlRepository.addUrl(savedAgent.id, urlId);
  // //       }
  // //     }

  // //     // Lưu thông tin vector store nếu có
  // //     if (createDto.vectorStoreId) {
  // //       try {
  // //         // Kiểm tra vectorStoreId có tồn tại trong database không
  // //         const vectorStore = await this.vectorStoreRepository.findOne({
  // //           where: { id: createDto.vectorStoreId }
  // //         });

  // //         if (!vectorStore) {
  // //           throw new AppException(
  // //             AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
  // //             `Vector store với ID ${createDto.vectorStoreId} không tồn tại`
  // //           );
  // //         }

  // //         // Nếu vector store tồn tại, lưu vào agent
  // //         savedAgent.vectorStoreId = createDto.vectorStoreId;
  // //         await this.agentRepository.save(savedAgent);
  // //       } catch (error) {
  // //         if (error instanceof AppException) {
  // //           throw error;
  // //         }
  // //         this.logger.error(`Lỗi khi kiểm tra vector store: ${error.message}`);
  // //         throw new AppException(
  // //           AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
  // //           `Không thể xác minh vector store: ${error.message}`
  // //         );
  // //       }
  // //     }

  // //     return {
  // //       id: savedAgent.id,
  // //       avatarUploadUrl,
  // //     };
  // //   } catch (error) {
  // //     if (error instanceof AppException) {
  // //       throw error;
  // //     }
  // //     this.logger.error(`Lỗi khi tạo agent: ${error.message}`);
  // //     throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED, error.message);
  // //   }
  // // }

  // /**
  //  * Tạo agent mới với cấu trúc modular theo TypeAgent config
  //  * @param userId ID của người dùng
  //  * @param createDto Thông tin agent mới với cấu trúc modular
  //  * @returns Thông tin tạo agent thành công
  //  */
  // @Transactional()
  // async createAgentModular(
  //   userId: number,
  //   createDto: CreateAgentModularDto,
  // ): Promise<CreateAgentResponseDto> {
  //   try {
  //     // Validate TypeAgent configuration và dữ liệu agent
  //     const typeAgentConfig = await this.typeAgentValidationHelper.validateAgentCreation(createDto);

  //     // Kiểm tra tên agent đã tồn tại cho user này chưa
  //     const nameExists = await this.agentRepository.existsByNameAndUserId(createDto.name, userId);
  //     if (nameExists) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_NAME_EXISTS);
  //     }

  //     // Tạo S3 key cho avatar nếu có
  //     let avatarS3Key: string | null = null;
  //     let avatarUploadUrl: string | null = null;
  //     if (createDto.avatarMimeType) {
  //       try {
  //         avatarS3Key = generateS3Key({
  //           baseFolder: userId.toString(),
  //           categoryFolder: CategoryFolderEnum.AGENT,
  //         });
  //         avatarUploadUrl = await this.s3Service.createPresignedWithID(
  //           avatarS3Key,
  //           TimeIntervalEnum.ONE_HOUR,
  //           ImageType.getType(createDto.avatarMimeType),
  //           FileSizeEnum.ONE_MB,
  //         );
  //       } catch (error) {
  //         throw new AppException(AGENT_ERROR_CODES.INVALID_S3_KEY, error.message);
  //       }
  //     }

  //     // Tạo agent mới với model config đơn giản
  //     const modelConfig: ModelConfig = {
  //       temperature: createDto.modelConfig.temperature,
  //       top_p: createDto.modelConfig.top_p,
  //       top_k: createDto.modelConfig.top_k,
  //       max_tokens: createDto.modelConfig.max_tokens,
  //     };

  //     const agent = this.agentRepository.create({
  //       name: createDto.name,
  //       modelConfig,
  //       instruction: createDto.instruction || null,
  //       status: AgentStatusEnum.PENDING,
  //       createdAt: Date.now(),
  //       updatedAt: Date.now(),
  //     });

  //     // Thêm avatar nếu có
  //     if (avatarS3Key) {
  //       agent.avatar = avatarS3Key;
  //     }

  //     // Thêm vector store nếu có
  //     if (createDto.vectorStoreId) {
  //       agent.vectorStoreId = createDto.vectorStoreId;
  //     }

  //     // Lưu agent
  //     const savedAgent = await this.agentRepository.save(agent);

  //     // Tạo agent user với profile nếu có
  //     let profileAgent: ProfileAgent | undefined;
  //     if (createDto.profile) {
  //       profileAgent = {
  //         gender: createDto.profile.gender ? GenderUtils.getGender(createDto.profile.gender.toUpperCase()) : undefined,
  //         dateOfBirth: createDto.profile.dateOfBirth ? new Date(createDto.profile.dateOfBirth) : undefined,
  //         position: createDto.profile.position,
  //         education: createDto.profile.education,
  //         skills: createDto.profile.skills,
  //         personality: createDto.profile.personality,
  //         languages: createDto.profile.languages,
  //         nations: createDto.profile.nations,
  //       };
  //     }

  //     const agentUser = this.agentUserRepository.create({
  //       id: savedAgent.id,
  //       userId,
  //       typeId: createDto.typeId,
  //       profile: profileAgent,
  //       // Lưu model fields từ createDto
  //       modelBaseId: createDto.model_base_id || null,
  //       modelFinetuningId: createDto.model_finetuning_id || null,
  //       modelId: createDto.model_id || null,
  //       providerId: createDto.provider_id || null,
  //     });

  //     // Lưu agent user
  //     await this.agentUserRepository.save(agentUser);

  //     // Xử lý các khối cấu hình theo TypeAgent config
  //     await this.processAgentBlocks(savedAgent.id, userId, createDto, typeAgentConfig);

  //     this.logger.log(`Tạo agent modular thành công: ${savedAgent.id} cho user ${userId}`);

  //     return {
  //       id: savedAgent.id,
  //       avatarUploadUrl,
  //     };
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(`Lỗi khi tạo agent modular: ${error.message}`, error.stack);
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED, error.message);
  //   }
  // }

  // /**
  //  * Xử lý các khối cấu hình agent theo TypeAgent config
  //  * @param agentId ID của agent
  //  * @param userId ID của người dùng
  //  * @param createDto Dữ liệu tạo agent
  //  * @param typeAgentConfig Cấu hình TypeAgent
  //  */
  // private async processAgentBlocks(
  //   agentId: string,
  //   userId: number,
  //   createDto: CreateAgentModularDto,
  //   typeAgentConfig: TypeAgentConfig,
  // ): Promise<void> {
  //   try {
  //     // Xử lý Output block nếu TypeAgent hỗ trợ
  //     if (typeAgentConfig.enableOutputToMessenger && createDto.outputMessenger) {
  //       await this.processOutputMessengerBlock(agentId, userId, createDto.outputMessenger);
  //     }

  //     // Xử lý Output block nếu TypeAgent hỗ trợ
  //     if (typeAgentConfig.enableOutputToWebsiteLiveChat && createDto.outputWebsite) {
  //       await this.processOutputWebsiteBlock(agentId, userId, createDto.outputWebsite);
  //     }

  //     // Xử lý Resources block nếu TypeAgent hỗ trợ
  //     if (typeAgentConfig.enableResourceUsage && createDto.resources) {
  //       await this.processResourcesBlock(agentId, userId, createDto.resources);
  //     }

  //     if (typeAgentConfig.enableTaskConversionTracking && createDto.conversion) {
  //       await this.processConversionBlock(agentId, userId, createDto.conversion);
  //     }

  //     // Xử lý Strategy block nếu TypeAgent hỗ trợ
  //     if (typeAgentConfig.enableDynamicStrategyExecution && createDto.strategy) {
  //       await this.processStrategyBlock(agentId, userId, createDto.strategy);
  //     }

  //     // Xử lý Multi Agent block nếu TypeAgent hỗ trợ
  //     if (typeAgentConfig.enableMultiAgentCollaboration && createDto.multiAgent) {
  //       await this.processMultiAgentBlock(agentId, userId, createDto.multiAgent);
  //     }
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi xử lý agent blocks: ${error.message}`, error.stack);
  //     throw error;
  //   }
  // }

  // /**
  //  * Xử lý Conversion block - lưu cấu hình chuyển đổi vào agents_user
  //  * @param agentId ID của agent
  //  * @param userId ID của người dùng
  //  * @param conversion Mảng cấu hình chuyển đổi
  //  */
  // private async processConversionBlock(agentId: string, userId: number, conversion: CreateConvertDto[]): Promise<void> {
  //   try {
  //     if (!conversion || conversion.length === 0) {
  //       this.logger.debug(`Không có conversion config cho agent ${agentId}`);
  //       return;
  //     }

  //     this.logger.log(`Xử lý Conversion cho agent ${agentId}: ${conversion.length} fields`);

  //     // Validate conversion config
  //     const validatedConfig = conversion.map(config => {
  //       // Validate required fields
  //       if (!config.name || !config.type) {
  //         throw new AppException(
  //           AGENT_ERROR_CODES.INVALID_CONVERSION_CONFIG,
  //           'Tên và kiểu dữ liệu là bắt buộc cho conversion config'
  //         );
  //       }

  //       // Validate type
  //       const validTypes = ['string', 'number', 'boolean', 'array', 'object'];
  //       if (!validTypes.includes(config.type)) {
  //         throw new AppException(
  //           AGENT_ERROR_CODES.INVALID_CONVERSION_CONFIG,
  //           `Kiểu dữ liệu không hợp lệ: ${config.type}`
  //         );
  //       }

  //       return {
  //         name: config.name,
  //         type: config.type,
  //         description: config.description || '',
  //         required: config.required || false,
  //       };
  //     });

  //     // Cập nhật convertConfig trong agents_user
  //     await this.agentUserRepository.update(
  //       { id: agentId },
  //       { convertConfig: validatedConfig }
  //     );

  //     this.logger.log(`Đã lưu conversion config cho agent ${agentId} thành công`);
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi xử lý conversion block cho agent ${agentId}: ${error.message}`, error.stack);
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     throw new AppException(
  //       AGENT_ERROR_CODES.CONVERSION_PROCESSING_FAILED,
  //       `Lỗi khi xử lý conversion block: ${error.message}`
  //     );
  //   }
  // }

  // /**
  //  * Xử lý Output Messenger block - liên kết Facebook Pages với Agent và subscribe app
  //  * @param agentId ID của agent
  //  * @param userId ID của người dùng
  //  * @param output Cấu hình output messenger
  //  */
  // private async processOutputMessengerBlock(agentId: string, userId: number, output: OutputMessengerBlockDto): Promise<void> {
  //   try {
  //     if (!output.facebookPageIds || output.facebookPageIds.length === 0) {
  //       this.logger.debug(`Không có Facebook Page IDs cho agent ${agentId}`);
  //       return;
  //     }

  //     this.logger.log(`Xử lý Facebook Pages cho agent ${agentId}: ${output.facebookPageIds.join(', ')}`);

  //     let processedCount = 0;
  //     let subscribedCount = 0;

  //     // Xử lý từng Facebook Page
  //     for (const pageId of output.facebookPageIds) {
  //       try {
  //         // Validate và lấy Facebook Page thuộc về user
  //         const facebookPage = await this.facebookPageRepository.findPageByUserIdAndPageId(userId, pageId);

  //         if (!facebookPage) {
  //           this.logger.warn(`Không tìm thấy hoặc không có quyền truy cập Facebook Page: ${pageId}`);
  //           continue;
  //         }

  //         // Kiểm tra page đã được tích hợp với agent khác chưa
  //         if (facebookPage.agentId && facebookPage.agentId !== agentId) {
  //           this.logger.warn(`Facebook Page ${pageId} đã được tích hợp với agent khác: ${facebookPage.agentId}`);
  //           continue;
  //         }

  //         // Nếu chưa tích hợp với agent này, thực hiện tích hợp
  //         if (!facebookPage.agentId) {
  //           // Subscribe app với Facebook Page
  //           try {
  //             const subscribeResult = await this.facebookService.subscribeApp(
  //               facebookPage.facebookPageId,
  //               facebookPage.pageAccessToken
  //             );

  //             if (subscribeResult.success) {
  //               subscribedCount++;
  //               this.logger.log(`Đã subscribe app cho Facebook Page: ${facebookPage.pageName} (${pageId})`);
  //             } else {
  //               this.logger.error(`Không thể subscribe app cho Facebook Page: ${pageId}`);
  //               continue;
  //             }
  //           } catch (subscribeError) {
  //             this.logger.error(`Lỗi khi subscribe app cho Facebook Page ${pageId}: ${subscribeError.message}`);
  //             continue;
  //           }

  //           // Cập nhật agent_id cho Facebook Page
  //           await this.facebookPageRepository.update(facebookPage.id, {
  //             agentId: agentId,
  //             isActive: true
  //           });

  //           processedCount++;
  //         } else {
  //           // Đã tích hợp rồi, chỉ cần đảm bảo isActive = true
  //           await this.facebookPageRepository.updateActiveStatus(facebookPage.id, true);
  //           processedCount++;
  //         }
  //       } catch (pageError) {
  //         this.logger.error(`Lỗi khi xử lý Facebook Page ${pageId}: ${pageError.message}`, pageError.stack);
  //         continue;
  //       }
  //     }

  //     this.logger.log(`Đã xử lý ${processedCount}/${output.facebookPageIds.length} Facebook Pages cho agent ${agentId} (${subscribedCount} pages được subscribe)`);
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi xử lý output messenger block cho agent ${agentId}: ${error.message}`, error.stack);
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     throw new AppException(
  //       AGENT_ERROR_CODES.OUTPUT_PROCESSING_FAILED,
  //       `Lỗi khi xử lý output messenger block: ${error.message}`
  //     );
  //   }
  // }

  // /**
  //  * Xử lý Output Website block - liên kết User Websites với Agent
  //  * @param agentId ID của agent
  //  * @param userId ID của người dùng
  //  * @param output Cấu hình output website
  //  */
  // private async processOutputWebsiteBlock(agentId: string, userId: number, output: OutputWebsiteBlockDto): Promise<void> {
  //   try {
  //     if (!output.userWebsiteIds || output.userWebsiteIds.length === 0) {
  //       this.logger.debug(`Không có website IDs cho agent ${agentId}`);
  //       return;
  //     }

  //     this.logger.log(`Xử lý User Websites cho agent ${agentId}: ${output.userWebsiteIds.join(', ')}`);

  //     // Validate và lấy danh sách websites thuộc về user
  //     const websites = await this.userWebsiteRepository.findByIdsAndUserId(output.userWebsiteIds, userId);

  //     // Kiểm tra tất cả website IDs có tồn tại và thuộc về user không
  //     const foundWebsiteIds = websites.map(w => w.id);
  //     const missingWebsiteIds = output.userWebsiteIds.filter(id => !foundWebsiteIds.includes(id));

  //     if (missingWebsiteIds.length > 0) {
  //       throw new AppException(
  //         AGENT_ERROR_CODES.WEBSITE_NOT_FOUND,
  //         `Không tìm thấy hoặc không có quyền truy cập websites: ${missingWebsiteIds.join(', ')}`
  //       );
  //     }

  //     // Cập nhật agent_id cho tất cả websites
  //     const updatePromises = websites.map(website =>
  //       this.userWebsiteRepository.updateAgentId(website.id, agentId)
  //     );

  //     await Promise.all(updatePromises);

  //     this.logger.log(`Đã liên kết ${websites.length} websites với agent ${agentId} thành công`);
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi xử lý output website block cho agent ${agentId}: ${error.message}`, error.stack);
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     throw new AppException(
  //       AGENT_ERROR_CODES.OUTPUT_PROCESSING_FAILED,
  //       `Lỗi khi xử lý output website block: ${error.message}`
  //     );
  //   }
  // }

  // /**
  //  * Xử lý Resources block - liên kết URLs, Media, Products với Agent
  //  * @param agentId ID của agent
  //  * @param userId ID của người dùng
  //  * @param resources Cấu hình resources
  //  */
  // private async processResourcesBlock(agentId: string, userId: number, resources: any): Promise<void> {
  //   try {
  //     let processedCount = 0;

  //     // Xử lý URLs
  //     if (resources.urlIds && resources.urlIds.length > 0) {
  //       this.logger.log(`Xử lý URLs cho agent ${agentId}: ${resources.urlIds.join(', ')}`);

  //       // Validate URLs ownership
  //       const existingUrls = await this.urlRepository.find({
  //         where: { id: In(resources.urlIds) }
  //       });

  //       const existingUrlIds = existingUrls.map(url => url.id);
  //       const missingUrlIds = resources.urlIds.filter(id => !existingUrlIds.includes(id));

  //       if (missingUrlIds.length > 0) {
  //         throw new AppException(
  //           AGENT_ERROR_CODES.URL_NOT_FOUND,
  //           `Không tìm thấy URLs: ${missingUrlIds.join(', ')}`
  //         );
  //       }

  //       // Validate ownership
  //       const unauthorizedUrls = existingUrls.filter(url => url.ownedBy !== userId);
  //       if (unauthorizedUrls.length > 0) {
  //         throw new AppException(
  //           AGENT_ERROR_CODES.URL_NOT_FOUND,
  //           `Không có quyền truy cập URLs: ${unauthorizedUrls.map(u => u.id).join(', ')}`
  //         );
  //       }

  //       // Add URLs to agent
  //       for (const urlId of resources.urlIds) {
  //         await this.agentUrlRepository.addUrl(agentId, urlId);
  //       }
  //       processedCount += resources.urlIds.length;
  //     }

  //     // Xử lý Media files
  //     if (resources.mediaIds && resources.mediaIds.length > 0) {
  //       this.logger.log(`Xử lý Media files cho agent ${agentId}: ${resources.mediaIds.join(', ')}`);

  //       // Validate Media ownership
  //       const existingMedias = await this.mediaRepository.find({
  //         where: { id: In(resources.mediaIds) }
  //       });

  //       const existingMediaIds = existingMedias.map(media => media.id);
  //       const missingMediaIds = resources.mediaIds.filter(id => !existingMediaIds.includes(id));

  //       if (missingMediaIds.length > 0) {
  //         throw new AppException(
  //           AGENT_ERROR_CODES.MEDIA_NOT_FOUND,
  //           `Không tìm thấy Media: ${missingMediaIds.join(', ')}`
  //         );
  //       }

  //       // Validate ownership
  //       const unauthorizedMedias = existingMedias.filter(media => media.ownedBy !== userId);
  //       if (unauthorizedMedias.length > 0) {
  //         throw new AppException(
  //           AGENT_ERROR_CODES.MEDIA_NOT_FOUND,
  //           `Không có quyền truy cập Media: ${unauthorizedMedias.map(m => m.id).join(', ')}`
  //         );
  //       }

  //       // Add Media to agent
  //       for (const mediaId of resources.mediaIds) {
  //         await this.agentMediaRepository.addMedia(agentId, mediaId);
  //       }
  //       processedCount += resources.mediaIds.length;
  //     }

  //     // Xử lý Products
  //     if (resources.productIds && resources.productIds.length > 0) {
  //       this.logger.log(`Xử lý Products cho agent ${agentId}: ${resources.productIds.join(', ')}`);

  //       // Convert productIds to numbers
  //       const productIdsNumber = resources.productIds.map(id => parseInt(id));

  //       // Validate Products ownership
  //       const existingProducts = await this.userProductRepository.find({
  //         where: { id: In(productIdsNumber) }
  //       });

  //       const existingProductIds = existingProducts.map(product => product.id.toString());
  //       const missingProductIds = resources.productIds.filter(id => !existingProductIds.includes(id));

  //       if (missingProductIds.length > 0) {
  //         throw new AppException(
  //           AGENT_ERROR_CODES.PRODUCT_NOT_FOUND,
  //           `Không tìm thấy Products: ${missingProductIds.join(', ')}`
  //         );
  //       }

  //       // Validate ownership
  //       const unauthorizedProducts = existingProducts.filter(product => product.createdBy !== userId);
  //       if (unauthorizedProducts.length > 0) {
  //         throw new AppException(
  //           AGENT_ERROR_CODES.PRODUCT_NOT_FOUND,
  //           `Không có quyền truy cập Products: ${unauthorizedProducts.map(p => p.id).join(', ')}`
  //         );
  //       }

  //       // Add Products to agent
  //       for (const productId of resources.productIds) {
  //         await this.agentProductRepository.addProduct(agentId, productId);
  //       }
  //       processedCount += resources.productIds.length;
  //     }

  //     this.logger.log(`Đã xử lý ${processedCount} resources cho agent ${agentId} thành công`);
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi xử lý resources block cho agent ${agentId}: ${error.message}`, error.stack);
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     throw new AppException(
  //       AGENT_ERROR_CODES.RESOURCES_PROCESSING_FAILED,
  //       `Lỗi khi xử lý resources block: ${error.message}`
  //     );
  //   }
  // }

  // /**
  //  * Xử lý Strategy block - liên kết Strategy với Agent
  //  * @param agentId ID của agent
  //  * @param userId ID của người dùng
  //  * @param strategy Cấu hình strategy
  //  */
  // private async processStrategyBlock(agentId: string, userId: number, strategy: StrategyBlockDto): Promise<void> {
  //   try {
  //     if (!strategy.strategyId) {
  //       this.logger.debug(`Không có strategyId cho agent ${agentId}`);
  //       return;
  //     }

  //     this.logger.log(`Xử lý Strategy cho agent ${agentId}: ${strategy.strategyId}`);

  //     // Validate strategyId format (should be number)
  //     const strategyIdNumber = parseInt(strategy.strategyId);
  //     if (isNaN(strategyIdNumber)) {
  //       throw new AppException(
  //         AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
  //         `Strategy ID không hợp lệ: ${strategy.strategyId}`
  //       );
  //     }

  //     // TODO: Validate strategy ownership and existence
  //     // Hiện tại chỉ cập nhật strategyId vào agents_user table
  //     // Có thể thêm validation với strategy tables sau

  //     // Cập nhật strategyId trong agents_user
  //     await this.agentUserRepository.update(
  //       { id: agentId },
  //       { strategyId: strategyIdNumber }
  //     );

  //     this.logger.log(`Đã liên kết strategy ${strategyIdNumber} với agent ${agentId} thành công`);
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi xử lý strategy block cho agent ${agentId}: ${error.message}`, error.stack);
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     throw new AppException(
  //       AGENT_ERROR_CODES.STRATEGY_PROCESSING_FAILED,
  //       `Lỗi khi xử lý strategy block: ${error.message}`
  //     );
  //   }
  // }

  // /**
  //  * Xử lý Multi Agent block
  //  */
  // private async processMultiAgentBlock(agentId: string, userId: number, multiAgent: MultiAgentBlockDto): Promise<void> {
  //   try {
  //     if (!multiAgent?.multiAgent || !Array.isArray(multiAgent.multiAgent) || multiAgent.multiAgent.length === 0) {
  //       this.logger.log(`Không có multi agent nào để xử lý cho agent ${agentId}`);
  //       return;
  //     }

  //     this.logger.log(`Xử lý Multi Agent cho agent ${agentId}: ${multiAgent.multiAgent.length} agents`);

  //     // Validate tất cả agent_id trước khi lưu
  //     const agentIds = multiAgent.multiAgent.map((item: any) => item.agent_id);

  //     // Kiểm tra các agent có tồn tại và thuộc về user không
  //     const existingAgents = await this.agentUserRepository.find({
  //       where: {
  //         id: In(agentIds),
  //         userId: userId,
  //       },
  //       select: ['id'],
  //     });

  //     const existingAgentIds = existingAgents.map(agent => agent.id);
  //     const nonExistingAgentIds = agentIds.filter((id: string) => !existingAgentIds.includes(id));

  //     if (nonExistingAgentIds.length > 0) {
  //       throw new AppException(
  //         AGENT_ERROR_CODES.AGENT_NOT_FOUND,
  //         `Không tìm thấy hoặc không có quyền truy cập các agent với ID: ${nonExistingAgentIds.join(', ')}`
  //       );
  //     }

  //     // Kiểm tra không được tự tham chiếu đến chính mình
  //     const selfReferencingIds = agentIds.filter((id: string) => id === agentId);
  //     if (selfReferencingIds.length > 0) {
  //       throw new AppException(
  //         AGENT_ERROR_CODES.INVALID_MULTI_AGENT_CONFIG,
  //         'Agent không thể tham chiếu đến chính mình trong multi agent'
  //       );
  //     }

  //     // Xóa các quan hệ multi agent cũ của agent này
  //     await this.userMultiAgentRepository.delete({
  //       parentAgentId: agentId,
  //     });

  //     // Tạo các quan hệ multi agent mới
  //     const multiAgentRelations = multiAgent.multiAgent.map((item: any) => ({
  //       parentAgentId: agentId,
  //       childAgentId: item.agent_id,
  //       prompt: item.prompt,
  //     }));

  //     await this.userMultiAgentRepository.save(multiAgentRelations);

  //     this.logger.log(`Đã lưu ${multiAgentRelations.length} quan hệ multi agent cho agent ${agentId}`);
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi xử lý Multi Agent block: ${error.message}`, error.stack);
  //     throw error;
  //   }
  // }

  // /**
  //  * Cập nhật agent
  //  * @param id ID của agent
  //  * @param userId ID của người dùng
  //  * @param updateDto Thông tin cập nhật
  //  * @returns Thông tin cập nhật agent thành công
  //  */
  // @Transactional()
  // async updateAgent(
  //   id: string,
  //   userId: number,
  //   updateDto: UpdateAgentDto,
  // ): Promise<UpdateAgentResponseDto> {
  //   try {
  //     // Lấy thông tin agent
  //     // Lấy thông tin agent từ repository
  //     const agent = await this.agentRepository.findOne({ where: { id } });
  //     if (!agent) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //     }

  //     // Lấy thông tin agent user
  //     const agentUser = await this.agentUserRepository.findOne({
  //       where: { id, userId },
  //     });
  //     if (!agentUser) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //     }

  //     // Cập nhật thông tin agent
  //     if (updateDto.name) {
  //       // Kiểm tra tên mới có trùng với agent khác của user không (trừ chính agent này)
  //       if (updateDto.name !== agent.name) {
  //         const nameExists = await this.agentRepository.existsByNameAndUserId(updateDto.name, userId);
  //         if (nameExists) {
  //           throw new AppException(AGENT_ERROR_CODES.AGENT_NAME_EXISTS);
  //         }
  //       }
  //       agent.name = updateDto.name;
  //     }
  //     if (updateDto.modelConfig) {
  //       // Kiểm tra model và provider_id
  //       if (typeof updateDto.modelConfig === 'object') {
  //         const modelConfigObj = updateDto.modelConfig;

  //         // Loại bỏ trường providerName nếu client gửi lên
  //         if ('providerName' in modelConfigObj) {
  //           const { providerName, ...validConfig } = modelConfigObj as any;
  //           updateDto.modelConfig = validConfig;
  //         }

  //         // Tạo ModelConfig từ updateDto.modelConfig (chỉ chứa parameters)
  //         const modelConfig: ModelConfig = {
  //           temperature: modelConfigObj.temperature,
  //           top_p: modelConfigObj.top_p,
  //           top_k: modelConfigObj.top_k,
  //           max_tokens: modelConfigObj.max_tokens,
  //         };

  //         agent.modelConfig = modelConfig;
  //       } else {
  //         agent.modelConfig = updateDto.modelConfig;
  //       }
  //     }
  //     if (updateDto.instruction !== undefined) {
  //       agent.instruction = updateDto.instruction;
  //     }
  //     agent.updatedAt = Date.now();

  //     // Lưu agent
  //     await this.agentRepository.save(agent);

  //     // Cập nhật thông tin agent user
  //     if (updateDto.profile) {
  //       // Chuyển đổi từ ProfileDto sang ProfileAgent
  //       const profileAgent = {
  //         ...agentUser.profile,
  //         gender: updateDto.profile.gender ? updateDto.profile.gender as any : undefined,
  //         dateOfBirth: updateDto.profile.dateOfBirth ? new Date(updateDto.profile.dateOfBirth) : undefined,
  //         position: updateDto.profile.position,
  //         // Chỉ sử dụng các trường có trong ProfileDto
  //         education: updateDto.profile.education,
  //         skills: updateDto.profile.skills,
  //         personality: updateDto.profile.personality,
  //         languages: updateDto.profile.languages,
  //         nations: updateDto.profile.nations,
  //       } as any; // Sử dụng type assertion để tránh lỗi TypeScript

  //       agentUser.profile = profileAgent;
  //       await this.agentUserRepository.save(agentUser);
  //     }

  //     // Tạo S3 key cho avatar mới nếu có
  //     let avatarUploadUrl = '';
  //     if (updateDto.avatarMimeType) {
  //       if (!userId || isNaN(userId)) {
  //         throw new AppException(AGENT_ERROR_CODES.INVALID_S3_KEY, 'ID người dùng không hợp lệ');
  //       }

  //       const avatarKey = generateS3Key({
  //         baseFolder: userId.toString(),
  //         categoryFolder: CategoryFolderEnum.AGENT,
  //       });

  //       // Cập nhật avatar key cho agent
  //       agent.avatar = avatarKey;
  //       await this.agentRepository.save(agent);

  //       // Tạo URL để upload avatar
  //       avatarUploadUrl = await this.s3Service.getDownloadUrl(
  //         avatarKey,
  //         TimeIntervalEnum.ONE_HOUR
  //       );
  //     }

  //     // Xử lý tài nguyên media nếu có
  //     if (updateDto.mediaIds && updateDto.mediaIds.length > 0) {
  //       // Kiểm tra tất cả media có tồn tại không
  //       const existingMedias = await this.mediaRepository.find({
  //         where: { id: In(updateDto.mediaIds) }
  //       });
  //       const existingMediaIds = existingMedias.map(media => media.id);
  //       const nonExistingMediaIds = updateDto.mediaIds.filter(id => !existingMediaIds.includes(id));

  //       if (nonExistingMediaIds.length > 0) {
  //         throw new AppException(
  //           AGENT_ERROR_CODES.MEDIA_NOT_FOUND,
  //           `Không tìm thấy media với ID: ${nonExistingMediaIds.join(', ')}`
  //         );
  //       }

  //       // Kiểm tra quyền truy cập media
  //       const unauthorizedMedias = existingMedias.filter(media => media.ownedBy !== userId);
  //       if (unauthorizedMedias.length > 0) {
  //         throw new AppException(
  //           AGENT_ERROR_CODES.MEDIA_NOT_FOUND,
  //           `Không có quyền truy cập media với ID: ${unauthorizedMedias.map(m => m.id).join(', ')}`
  //         );
  //       }

  //       // Thêm từng media vào agent
  //       for (const mediaId of updateDto.mediaIds) {
  //         await this.agentMediaRepository.addMedia(agent.id, mediaId);
  //       }
  //     }

  //     // Xử lý tài nguyên product nếu có
  //     if (updateDto.productIds && updateDto.productIds.length > 0) {
  //       // Chuyển đổi productIds từ string sang number
  //       const productIdsNumber = updateDto.productIds.map(id => parseInt(id));

  //       // Kiểm tra tất cả product có tồn tại không
  //       const existingProducts = await this.userProductRepository.find({
  //         where: { id: In(productIdsNumber) }
  //       });

  //       const existingProductIds = existingProducts.map(product => product.id.toString());
  //       const nonExistingProductIds = updateDto.productIds.filter(id => !existingProductIds.includes(id));

  //       if (nonExistingProductIds.length > 0) {
  //         throw new AppException(
  //           AGENT_ERROR_CODES.PRODUCT_NOT_FOUND,
  //           `Không tìm thấy sản phẩm với ID: ${nonExistingProductIds.join(', ')}`
  //         );
  //       }

  //       // Kiểm tra quyền truy cập product
  //       const unauthorizedProducts = existingProducts.filter(product => product.createdBy !== userId);
  //       if (unauthorizedProducts.length > 0) {
  //         throw new AppException(
  //           AGENT_ERROR_CODES.PRODUCT_NOT_FOUND,
  //           `Không có quyền truy cập sản phẩm với ID: ${unauthorizedProducts.map(p => p.id).join(', ')}`
  //         );
  //       }

  //       // Thêm từng product vào agent
  //       for (const productId of updateDto.productIds) {
  //         await this.agentProductRepository.addProduct(agent.id, productId);
  //       }
  //     }

  //     // Xử lý tài nguyên url nếu có
  //     if (updateDto.urlIds && updateDto.urlIds.length > 0) {
  //       // Kiểm tra tất cả URL có tồn tại không
  //       const existingUrls = await this.urlRepository.find({
  //         where: { id: In(updateDto.urlIds) }
  //       });
  //       const existingUrlIds = existingUrls.map(url => url.id);
  //       const nonExistingUrlIds = updateDto.urlIds.filter(id => !existingUrlIds.includes(id));

  //       if (nonExistingUrlIds.length > 0) {
  //         throw new AppException(
  //           AGENT_ERROR_CODES.URL_NOT_FOUND,
  //           `Không tìm thấy URL với ID: ${nonExistingUrlIds.join(', ')}`
  //         );
  //       }

  //       // Kiểm tra quyền truy cập URL
  //       const unauthorizedUrls = existingUrls.filter(url => url.ownedBy !== userId);
  //       if (unauthorizedUrls.length > 0) {
  //         throw new AppException(
  //           AGENT_ERROR_CODES.URL_NOT_FOUND,
  //           `Không có quyền truy cập URL với ID: ${unauthorizedUrls.map(u => u.id).join(', ')}`
  //         );
  //       }

  //       // Thêm từng URL vào agent
  //       for (const urlId of updateDto.urlIds) {
  //         await this.agentUrlRepository.addUrl(agent.id, urlId);
  //       }
  //     }

  //     // Cập nhật thông tin vector store nếu có
  //     if (updateDto.vectorStoreId !== undefined) {
  //       // Ở đây có thể thêm logic kiểm tra vectorStoreId có tồn tại không
  //       // Ví dụ: gọi API hoặc kiểm tra trong database

  //       // Giả sử chúng ta có một danh sách các vector store ID hợp lệ
  //       // const validVectorStoreIds = ['vector-store-1', 'vector-store-2'];
  //       // if (updateDto.vectorStoreId && !validVectorStoreIds.includes(updateDto.vectorStoreId)) {
  //       //   throw new AppException(AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND, 'Vector store không tồn tại');
  //       // }

  //       agent.vectorStoreId = updateDto.vectorStoreId;
  //       await this.agentRepository.save(agent);
  //     }

  //     return {
  //       id: agent.id,
  //       avatarUploadUrl,
  //     };
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(`Lỗi khi cập nhật agent: ${error.message}`);
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED, error.message);
  //   }
  // }

  // /**
  //  * Xóa agent
  //  * @param id ID của agent
  //  * @param userId ID của người dùng
  //  */
  // @Transactional()
  // async deleteAgent(id: string, userId: number): Promise<void> {
  //   try {
  //     // Sử dụng repository để xóa mềm agent
  //     await this.agentUserRepository.softDeleteAgent(id, userId);
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(`Lỗi khi xóa agent: ${error.message}`);
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED, error.message);
  //   }
  // }

  // /**
  //  * Đảo ngược trạng thái hoạt động của agent
  //  * @param id ID của agent
  //  * @param userId ID của người dùng
  //  * @returns Thông tin cập nhật trạng thái thành công
  //  */
  // @Transactional()
  // async updateAgentActive(
  //   id: string,
  //   userId: number,
  // ): Promise<{ id: string; active: boolean }> {
  //   try {
  //     // Kiểm tra agent có tồn tại không
  //     const result = await this.agentUserRepository.findAgentByIdAndUserId(id, userId);
  //     if (!result) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //     }

  //     // Lấy trạng thái hiện tại và đảo ngược nó
  //     const currentActive = result.agentUser.active || false;
  //     const newActive = !currentActive;

  //     // Sử dụng repository để cập nhật trạng thái hoạt động
  //     await this.agentUserRepository.updateAgentActive(id, userId, newActive);

  //     return {
  //       id,
  //       active: newActive,
  //     };
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(`Lỗi khi cập nhật trạng thái hoạt động của agent: ${error.message}`);
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED, error.message);
  //   }
  // }

  // /**
  //  * Cập nhật loại agent cho một agent
  //  * @param id ID của agent
  //  * @param userId ID của người dùng
  //  * @param updateDto Thông tin cập nhật loại agent
  //  */
  // @Transactional()
  // async updateAgentType(
  //   id: string,
  //   userId: number,
  //   updateDto: UpdateAgentTypeDto,
  // ): Promise<void> {
  //   try {
  //     // Kiểm tra agent có tồn tại không
  //     const result = await this.agentUserRepository.findAgentByIdAndUserId(id, userId);
  //     if (!result) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //     }

  //     // Kiểm tra loại agent có tồn tại không
  //     const typeAgent = await this.typeAgentRepository.findById(updateDto.typeId);
  //     if (!typeAgent) {
  //       throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
  //     }

  //     // Sử dụng repository để cập nhật loại agent
  //     await this.agentUserRepository.updateAgentType(id, userId, updateDto.typeId);
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(`Lỗi khi cập nhật loại agent: ${error.message}`);
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED, error.message);
  //   }
  // }

  // /**
  //  * Cập nhật kho vector cho một agent
  //  * @param id ID của agent
  //  * @param userId ID của người dùng
  //  * @param updateDto Thông tin cập nhật kho vector
  //  */
  // @Transactional()
  // async updateAgentVectorStore(
  //   id: string,
  //   userId: number,
  //   updateDto: UpdateAgentVectorStoreDto,
  // ): Promise<void> {
  //   try {
  //     // Lấy thông tin agent - chỉ select các trường cần thiết
  //     const agent = await this.agentRepository.createBaseQuery()
  //       .select(['agent.id'])
  //       .where('agent.id = :id', { id })
  //       .andWhere('agent.deletedAt IS NULL')
  //       .getOne();
  //     if (!agent) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //     }

  //     // Lấy thông tin agent user - chỉ select các trường cần thiết
  //     const agentUser = await this.agentUserRepository.createBaseQuery()
  //       .select(['agentUser.id', 'agentUser.userId'])
  //       .where('agentUser.id = :id', { id })
  //       .andWhere('agentUser.userId = :userId', { userId })
  //       .getOne();
  //     if (!agentUser) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //     }

  //     // Kiểm tra vector store có tồn tại không
  //     const vectorStore = await this.vectorStoreRepository.findOne({
  //       where: { id: updateDto.vector_store_id }
  //     });

  //     if (!vectorStore) {
  //       throw new AppException(
  //         AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
  //         `Vector store với ID ${updateDto.vector_store_id} không tồn tại`
  //       );
  //     }

  //     // Cập nhật vector store cho agent
  //     agent.vectorStoreId = updateDto.vector_store_id;
  //     await this.agentRepository.save(agent);

  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(`Lỗi khi cập nhật kho vector: ${error.message}`);
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED, error.message);
  //   }
  // }

  // /**
  //  * Lấy thống kê agent
  //  * @param id ID của agent
  //  * @param userId ID của người dùng
  //  * @param queryDto Tham số truy vấn
  //  * @returns Thống kê agent
  //  */
  // async getAgentStatistics(
  //   id: string,
  //   userId: number,
  //   _queryDto: AgentStatisticsQueryDto, // Thêm dấu gạch dưới để đánh dấu tham số không sử dụng
  // ): Promise<AgentStatisticsResponseDto> {
  //   try {
  //     // Lấy thông tin agent
  //     const agent = await this.agentRepository.findOne({ where: { id } });
  //     if (!agent) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //     }

  //     // Lấy thông tin agent user
  //     const agentUser = await this.agentUserRepository.findOne({
  //       where: { id, userId },
  //     });
  //     if (!agentUser) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //     }

  //     // TODO: Implement lấy thống kê agent
  //     // Đây là dữ liệu mẫu, cần thay thế bằng dữ liệu thực tế
  //     return {
  //       totalConversations: 120,
  //       totalMessages: 1850,
  //       averageMessagesPerConversation: 15.4,
  //       averageResponseTime: 1.2,
  //       dailyStats: [
  //         {
  //           date: 1672531200000,
  //           conversations: 10,
  //           messages: 150,
  //         },
  //         {
  //           date: 1672617600000,
  //           conversations: 15,
  //           messages: 220,
  //         },
  //       ],
  //     };
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(`Lỗi khi lấy thống kê agent: ${error.message}`);
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_STATISTICS_FAILED, error.message);
  //   }
  // }

  // /**
  //  * Chuyển đổi từ entity sang DTO chi tiết
  //  * @param agent Entity Agent
  //  * @param agentUser Entity AgentUser
  //  * @returns AgentDetailDto
  //  */
  // private async mapToAgentDetailDto(agent: Agent, agentUser: AgentUser): Promise<AgentDetailDto> {
  //   // Sử dụng resolved model info từ repository (đã JOIN trong query)
  //   const resolvedModelId = (agentUser as any).resolvedModelId || '';
  //   const resolvedProvider = (agentUser as any).resolvedProvider || 'unknown';

  //   // Chuyển đổi ModelConfig sang ModelConfigDto (đã loại bỏ deprecated fields)
  //   const modelConfigDto = {
  //     temperature: agent.modelConfig?.temperature || 0.7,
  //     top_p: agent.modelConfig?.top_p || 0.9,
  //     top_k: agent.modelConfig?.top_k || 40,
  //     max_tokens: agent.modelConfig?.max_tokens || 1000,
  //   } as ModelConfigDto;

  //   // Chuyển đổi ProfileAgent sang ProfileDto
  //   const profileDto = {
  //     gender: agentUser.profile?.gender?.toString() || '',
  //     dateOfBirth: agentUser.profile?.dateOfBirth instanceof Date
  //       ? agentUser.profile.dateOfBirth.getTime()
  //       : (typeof agentUser.profile?.dateOfBirth === 'string'
  //         ? new Date(agentUser.profile.dateOfBirth).getTime()
  //         : 946684800000), // Mặc định 01/01/2000
  //     position: agentUser.profile?.position || '',
  //     education: agentUser.profile?.education || '',
  //     skills: agentUser.profile?.skills || [],
  //     personality: agentUser.profile?.personality || [],
  //     languages: agentUser.profile?.languages || [],
  //     nations: agentUser.profile?.nations || '',
  //   } as ProfileDto; // Sử dụng type assertion để tránh lỗi TypeScript

  //   // Tạo URL CDN cho avatar nếu có
  //   let avatarUrl = agent.avatar;
  //   if (agent.avatar) {
  //     try {
  //       avatarUrl = this.cdnService.generateUrlView(agent.avatar, TimeIntervalEnum.ONE_DAY);
  //     } catch (error) {
  //       this.logger.warn(`Không thể tạo URL CDN cho avatar: ${error.message}`);
  //     }
  //   }

  //   // Lấy tên của loại agent nếu có
  //   let typeName = '';
  //   if (agentUser.typeAgent && agentUser.typeAgent.name) {
  //     typeName = agentUser.typeAgent.name;
  //   }

  //   // Tạo vector store
  //   const vectorStores = agent.vectorStoreId ? {
  //     id: agent.vectorStoreId,
  //     name: agent.vectorStoreId // Using ID as name since we don't have the actual name
  //   } : undefined;

  //   // Tìm rank phù hợp dựa trên exp
  //   const exp = agentUser.exp || 0;
  //   const ranks = await this.agentRankRepository.findAll();
  //   const rank = ranks.find(r => exp >= r.minExp && exp < r.maxExp) || ranks[0];

  //   // Tạo URL CDN cho badge nếu có
  //   let badgeUrl = '';
  //   if (rank && rank.badge) {
  //     try {
  //       badgeUrl = this.cdnService.generateUrlView(rank.badge, TimeIntervalEnum.ONE_DAY) || '';
  //     } catch (error) {
  //       this.logger.warn(`Không thể tạo URL CDN cho badge: ${error.message}`);
  //     }
  //   }

  //   return {
  //     id: agent.id,
  //     name: agent.name,
  //     avatar: avatarUrl,
  //     typeId: agentUser.typeId,
  //     typeName: typeName,
  //     modelConfig: modelConfigDto,
  //     // Thêm model information mới
  //     model_id: resolvedModelId,
  //     provider_type: resolvedProvider,
  //     model_base_id: agentUser.modelBaseId || undefined,
  //     model_finetuning_id: agentUser.modelFinetuningId || undefined,
  //     provider_id: agentUser.providerId || undefined,
  //     vector_store_id: agent.vectorStoreId || undefined,
  //     instruction: agent.instruction || '',
  //     profile: profileDto,
  //     active: agentUser.active || false,
  //     vectorStores: vectorStores,
  //     createdAt: agent.createdAt,
  //     updatedAt: agent.updatedAt,
  //     // Thêm các trường mới
  //     exp: agentUser.exp || 0,
  //     expMax: rank ? rank.maxExp : 100,
  //     level: rank ? rank.id : 1,
  //     badge_url: badgeUrl,
  //   };
  // }
}
