import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  CreateMcpSystemDto,
  UpdateMcpSystemDto,
  McpSystemQueryDto,
  McpSystemDetailDto,
  McpSystemListItemDto,
} from '../dto/mcp-system';
import { McpSystemAdminService } from '../services/mcp-system-admin.service';

/**
 * Controller xử lý các endpoint liên quan đến MCP Systems cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_AGENT_MCP_SYSTEM)
@ApiExtraModels(ApiResponseDto, PaginatedResult, McpSystemDetailDto, McpSystemListItemDto)
@Controller('admin/mcp-systems')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class McpSystemAdminController {
  constructor(private readonly mcpSystemAdminService: McpSystemAdminService) {}

  /**
   * Tạo mới MCP system
   * @param createDto Thông tin MCP system cần tạo
   * @returns Thông tin MCP system đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới MCP system' })
  @ApiResponse({
    status: 201,
    description: 'Tạo mới MCP system thành công',
    schema: ApiResponseDto.getSchema(McpSystemDetailDto),
  })
  async createMcpSystem(
    @Body() createDto: CreateMcpSystemDto
  ): Promise<ApiResponseDto<McpSystemDetailDto>> {
    const result = await this.mcpSystemAdminService.createMcpSystem(createDto);
    return ApiResponseDto.created(result);
  }

  /**
   * Cập nhật MCP system
   * @param id ID của MCP system cần cập nhật
   * @param updateDto Thông tin cần cập nhật
   * @returns Thông tin MCP system sau khi cập nhật
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật MCP system' })
  @ApiParam({ name: 'id', description: 'ID của MCP system', type: String })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật MCP system thành công',
    schema: ApiResponseDto.getSchema(McpSystemDetailDto),
  })
  async updateMcpSystem(
    @Param('id') id: string,
    @Body() updateDto: UpdateMcpSystemDto,
  ): Promise<ApiResponseDto<McpSystemDetailDto>> {
    const result = await this.mcpSystemAdminService.updateMcpSystem(id, updateDto);
    return ApiResponseDto.updated(result);
  }

  /**
   * Xóa MCP system
   * @param id ID của MCP system cần xóa
   * @returns Thông báo xóa thành công
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa MCP system' })
  @ApiParam({ name: 'id', description: 'ID của MCP system', type: String })
  @ApiResponse({
    status: 200,
    description: 'Xóa MCP system thành công',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  async deleteMcpSystem(@Param('id') id: string): Promise<ApiResponseDto<boolean>> {
    await this.mcpSystemAdminService.deleteMcpSystem(id);
    return ApiResponseDto.deleted(true);
  }

  /**
   * Lấy danh sách MCP systems có phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách MCP systems có phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách MCP systems' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách MCP systems thành công',
    schema: ApiResponseDto.getPaginatedSchema(McpSystemListItemDto),
  })
  async getMcpSystems(
    @Query() queryDto: McpSystemQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<McpSystemListItemDto>>> {
    const result = await this.mcpSystemAdminService.getMcpSystems(queryDto);
    return ApiResponseDto.paginated(result);
  }
}
