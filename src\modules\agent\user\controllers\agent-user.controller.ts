import { ApiResponseDto } from '@common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { AgentUserService } from '@modules/agent/user/services';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { JWTPayload } from '@modules/auth/interfaces/jwt-payload.interface';
import {
  Body,
  Controller,
  Post,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import {
  CreateAgentDto,
  CreateAgentResponseDto
} from '../dto/agent';

/**
 * Controller xử lý các API endpoint cho Agent của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  CreateAgentResponseDto,
  ApiResponseDto
)
export class AgentUserController {
  constructor(private readonly agentUserService: AgentUserService) { }

  /**
   * Tạo agent mới theo cấu trúc TypeAgentConfig
   * @param createDto Thông tin agent cần tạo
   * @param user Thông tin người dùng từ JWT
   * @returns Thông tin agent đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo agent mới theo cấu trúc TypeAgentConfig' })
  @ApiResponse({
    status: 201,
    description: 'Tạo agent thành công',
    schema: ApiResponseDto.getSchema(CreateAgentResponseDto),
  })
  async createAgent(
    @Body() createDto: CreateAgentDto,
    @CurrentUser() user: JWTPayload,
  ): Promise<ApiResponseDto<CreateAgentResponseDto>> {
    const result = await this.agentUserService.createAgent(user.id, createDto);
    return ApiResponseDto.created(result);
  }
}
