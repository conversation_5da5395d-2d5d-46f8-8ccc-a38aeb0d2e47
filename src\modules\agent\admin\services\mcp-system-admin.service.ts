import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions/app.exception';
import { PaginatedResult } from '@common/response';
import { McpSystemsRepository } from '@modules/agent/repositories';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error-codes';
import {
  CreateMcpSystemDto,
  UpdateMcpSystemDto,
  McpSystemQueryDto,
  McpSystemListItemDto
} from '../dto/mcp-system';
import { McpSystemMapper } from '../mappers/mcp-system.mapper';

/**
 * Service xử lý logic nghiệp vụ cho MCP Systems (Admin)
 */
@Injectable()
export class McpSystemAdminService {
  private readonly logger = new Logger(McpSystemAdminService.name);

  constructor(
    private readonly mcpSystemsRepository: McpSystemsRepository,
  ) {}

  /**
   * Tạo mới MCP system
   * @param createDto Thông tin MCP system cần tạo
   * @returns Thông tin MCP system đã tạo
   */
  async createMcpSystem(createDto: CreateMcpSystemDto): Promise<McpSystemListItemDto> {
    try {
      // Kiểm tra tên server đã tồn tại chưa
      const existingSystem = await this.mcpSystemsRepository.findByNameServer(createDto.nameServer);
      if (existingSystem) {
        throw new AppException(AGENT_ERROR_CODES.MCP_SYSTEM_NAME_EXISTS);
      }

      // Tạo mới MCP system
      const mcpSystem = this.mcpSystemsRepository.create({
        nameServer: createDto.nameServer,
        description: createDto.description,
        config: createDto.config,
      });

      const savedSystem = await this.mcpSystemsRepository.save(mcpSystem);

      this.logger.log(`Đã tạo MCP system mới: ${savedSystem.id}`);
      return McpSystemMapper.toListItemDto(savedSystem);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo MCP system: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.MCP_SYSTEM_CREATION_FAILED);
    }
  }

  /**
   * Cập nhật MCP system
   * @param id ID của MCP system cần cập nhật
   * @param updateDto Thông tin cần cập nhật
   * @returns Thông tin MCP system sau khi cập nhật
   */
  async updateMcpSystem(id: string, updateDto: UpdateMcpSystemDto): Promise<McpSystemListItemDto> {
    try {
      // Kiểm tra MCP system có tồn tại không
      const existingSystem = await this.mcpSystemsRepository.findById(id);
      if (!existingSystem) {
        throw new AppException(AGENT_ERROR_CODES.MCP_SYSTEM_NOT_FOUND);
      }

      // Kiểm tra tên server mới có trùng với system khác không
      if (updateDto.nameServer && updateDto.nameServer !== existingSystem.nameServer) {
        const nameExists = await this.mcpSystemsRepository.isNameServerExists(updateDto.nameServer, id);
        if (nameExists) {
          throw new AppException(AGENT_ERROR_CODES.MCP_SYSTEM_NAME_EXISTS);
        }
      }

      // Cập nhật thông tin
      Object.assign(existingSystem, updateDto);
      const updatedSystem = await this.mcpSystemsRepository.save(existingSystem);

      this.logger.log(`Đã cập nhật MCP system: ${id}`);
      return McpSystemMapper.toListItemDto(updatedSystem);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật MCP system ${id}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.MCP_SYSTEM_UPDATE_FAILED);
    }
  }

  /**
   * Xóa MCP system
   * @param id ID của MCP system cần xóa
   */
  async deleteMcpSystem(id: string): Promise<void> {
    try {
      // Kiểm tra MCP system có tồn tại không
      const existingSystem = await this.mcpSystemsRepository.findById(id);
      if (!existingSystem) {
        throw new AppException(AGENT_ERROR_CODES.MCP_SYSTEM_NOT_FOUND);
      }

      await this.mcpSystemsRepository.remove(existingSystem);
      this.logger.log(`Đã xóa MCP system: ${id}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa MCP system ${id}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.MCP_SYSTEM_DELETE_FAILED);
    }
  }

  /**
   * Lấy danh sách MCP systems có phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách MCP systems có phân trang
   */
  async getMcpSystems(queryDto: McpSystemQueryDto): Promise<PaginatedResult<McpSystemListItemDto>> {
    const result = await this.mcpSystemsRepository.findPaginated(
      queryDto.page,
      queryDto.limit,
      queryDto.search,
      queryDto.sortBy,
      queryDto.sortDirection,
    );

    return {
      items: McpSystemMapper.toListItemDtos(result.items),
      meta: result.meta,
    };
  }
}
