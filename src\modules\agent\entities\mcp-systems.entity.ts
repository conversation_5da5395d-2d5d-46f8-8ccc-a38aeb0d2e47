import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng mcp_systems trong cơ sở dữ liệu
 * Quản lý các hệ thống MCP (Model Context Protocol)
 */
@Entity('mcp_systems')
export class McpSystems {
  /**
   * UUID của MCP system
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên server MCP
   */
  @Column({
    name: 'name_server',
    type: 'varchar',
    length: 255,
    unique: true,
    comment: 'Tên server MCP'
  })
  nameServer: string;

  /**
   * <PERSON>ô tả về MCP system
   */
  @Column({
    type: 'text',
    nullable: true,
    comment: 'Mô tả về MCP system'
  })
  description?: string;

  /**
   * Cấu hình MCP dạng JSONB
   */
  @Column({
    type: 'jsonb',
    comment: 'Cấu hình MCP dạng JSONB'
  })
  config: Record<string, any>;
}
